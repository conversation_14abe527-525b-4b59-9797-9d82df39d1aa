import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';

export interface CreatedEventData {
  id: string;
  title: string;
  category_id: string;
  category_name?: string;
  date_from: string;
  date_to: string;
  description: string;
  location_address: string;
  banner_image_id: string | null;
  status?: string;
  user_id?: string;
  created_at: string;
  updated_at: string;
}

// Complete event data interface that matches the full API response
export interface CompleteEventData {
  // Basic event info
  id: string;
  title: string;
  description: string;
  category_id: string;
  category_name?: string;
  event_status: string;
  visibility: string;

  // Dates
  date_from: string;
  date_to: string;
  created_at: string;
  updated_at: string;

  // Location
  location_name: string;
  location_address: string;
  location_lat?: number;
  location_long?: number;

  // Delivery
  delivery_address?: string;
  delivery_address_id?: string;
  delivery_address_name?: string;
  delivery_address_lat?: number;
  delivery_address_long?: number;

  // Images
  banner_image_id?: string | null;
  banner_preview_key?: string;
  banner_preview_url?: string;
  images?: Array<{
    id: string;
    preview_key: string;
    preview_url: string;
  }>;

  // Invitation
  iv_preview_key?: string;
  iv_preview_url?: string;
  iv_template_id?: string;
  iv_template_modifications?: any;
  iv_type?: string;

  // Gift registry
  gift_registry_status?: string;
  gift_registry_title?: string;
  most_wanted_gift_id?: string;
  gift_count?: {
    total: number;
    pending: number;
    approved: number;
  };

  // Guest management
  guestlist_status?: string;
  guest_count?: {
    total: number;
    pending: number;
    confirmed: number;
    declined: number;
  };

  // Other
  reminders?: Array<{
    id: string;
    type: string;
    scheduled_at: string;
    status: string;
  }>;
  user_id: string;
}

interface EventsMetaData {
  from: number | null;
  to: number | null;
  page: number;
  per_page: number;
  previous_page: boolean;
  next_page: boolean;
  page_count: number;
  total: number;
}

type EventState = {
  createdEventData: CreatedEventData | null;
  userEvents: CreatedEventData[];
  selectedEvent: CreatedEventData | CompleteEventData | null;
  completeSelectedEvent: CompleteEventData | null;
  eventsMeta: EventsMetaData | null;
  setCreatedEventData: (eventData: CreatedEventData) => void;
  setUserEvents: (events: CreatedEventData[], meta?: EventsMetaData) => void;
  setSelectedEvent: (
    event: CreatedEventData | CompleteEventData | null
  ) => void;
  setCompleteSelectedEvent: (event: CompleteEventData | null) => void;
  clearCreatedEventData: () => void;
  clearAllEventData: () => void;
};

export const useEventStore = create<EventState>()(
  persist(
    (set, get) => ({
      createdEventData: null,
      userEvents: [],
      selectedEvent: null,
      completeSelectedEvent: null,
      eventsMeta: null,
      setCreatedEventData: (eventData) => set({ createdEventData: eventData }),
      setUserEvents: (events, meta) => {
        const currentState = get();
        const newSelectedEvent =
          currentState.selectedEvent || (events.length > 0 ? events[0] : null);

        set({
          userEvents: events,
          eventsMeta: meta || null,
          selectedEvent: newSelectedEvent,
        });
      },
      setSelectedEvent: (event) => set({ selectedEvent: event }),
      setCompleteSelectedEvent: (event) =>
        set({ completeSelectedEvent: event }),
      clearCreatedEventData: () => set({ createdEventData: null }),
      clearAllEventData: () =>
        set({
          createdEventData: null,
          userEvents: [],
          selectedEvent: null,
          completeSelectedEvent: null,
          eventsMeta: null,
        }),
    }),
    {
      name: 'event-store',
      storage: createJSONStorage(() => localStorage),
    }
  )
);
