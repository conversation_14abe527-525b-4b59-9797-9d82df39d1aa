import { EventParkAPI } from '../event-park-api';

export interface CashGiftItem {
  amount: string;
  description: string;
  is_crowd_gift?: boolean;
}
export interface ItemsGift {
  link: string;
  description: string;
  name: string;
  price: string;
  quantity: number;
}

export interface CreateCashGiftPayload {
  gifts: CashGiftItem[];
}
export interface CreateItemsGiftPayload {
  gifts: ItemsGift[];
}

export interface ItemsParams {
  page?: number;
  per_page?: number;
  from?: string;
  to?: string;
  status?: string;
}

export const GiftRegistryServices = {
  createCashGift: async (eventId: string, payload: CreateCashGiftPayload) => {
    return await EventParkAPI().post(
      `/v1/user/events/${eventId}/gifts/cash`,
      payload
    );
  },
  createItemsGift: async (eventId: string, payload: CreateItemsGiftPayload) => {
    return await EventParkAPI().post(
      `/v1/user/events/${eventId}/gifts/item`,
      payload
    );
  },
  getPayoutBanks: async (currencyCode = 'ngn') => {
    return await EventParkAPI().get(`/v1/user/payout/banks/${currencyCode}`);
  },
  resolveUserPayoutBank: async (payload: {
    bank_code: string;
    account_number: string;
    currency_code: string;
  }) => {
    return await EventParkAPI().post(
      '/v1/user/payout/accounts/resolve',
      payload
    );
  },
  createUserPayoutBank: async (payload: {
    bank_code: string;
    account_number: string;
  }) => {
    return await EventParkAPI().post('/v1/user/payout/accounts', payload);
  },
  createUserWallet: async (payload: { currency_code: string }) => {
    return await EventParkAPI().post('/v1/user/wallets', payload);
  },
  createAddress: async (payload: { location_place_id: string }) => {
    return await EventParkAPI().post('/v1/user/addresses', payload);
  },
  getCashGifts: async (id: string, params?: ItemsParams) => {
    return await EventParkAPI().get(`/v1/user/events/${id}/gifts/cash`, {
      params,
    });
  },
  getGiftItems: async (id: string, params?: ItemsParams) => {
    return await EventParkAPI().get(`/v1/user/events/${id}/gifts/item`, {
      params,
    });
  },
};
