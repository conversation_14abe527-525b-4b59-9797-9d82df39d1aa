import { useState } from "react";
import { ArrowCircleRight2, CloseCircle, Tag2 } from "iconsax-react";
import { Icon } from "../../../components/icons/icon";
import { Success } from "./success";
import stackMoney from "../../../assets/images/stack-money2.svg";

interface GiftItem {
  id: number;
  name: string;
  description?: string;
  price?: number | string;
  image: string | File;
  quantity?: number;
  link?: string;
  mostWanted?: boolean;
}

interface CashGift {
  id: number;
  amount: string;
  description: string;
}

interface RegistryData {
  registryTitle?: string;
  giftTypes?: string[];
  giftItems?: GiftItem[];
  cashGifts?: CashGift[];
  bank?: string;
  accountNumber?: string;
  accountName?: string;
  location?: string;
}

interface CombinedPreviewProps {
  initialData?: RegistryData;
  onClose?: () => void;
}

export const CombinedPreview = ({
  initialData = {},
  onClose = () => {},
}: CombinedPreviewProps) => {
  const [open, setOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<"gift-items" | "cash-gifts">(
    "gift-items"
  );

  // Debug logs
  console.log("CombinedPreview initialData:", initialData);
  console.log("Cash gifts:", initialData.cashGifts);
  console.log("Gift items:", initialData.giftItems);

  const accountDetails = {
    bank: initialData.bank || "GTBank",
    accountNumber: initialData.accountNumber || "**********",
    accountName: initialData.accountName || "ADE BOLUWATIFE",
    location:
      initialData.location || "Lekki Conservation Center, Lekki, Lagos State",
  };

  return (
    <>
      <div className="bg-white min-h-screen mb-40 px-4 lg:px-0">
        <div className="max-w-[560px] mx-auto">
          <div className="flex justify-between items-center">
            <h1 className="text-[28px] font-semibold mt-5 italic mb-4">
              {initialData.registryTitle || "Oladele's birthday gifts"}
            </h1>
            <div className="flex justify-center  relative md:-right-[40px]">
              <button
                type="button"
                onClick={() => setOpen(true)}
                className="bg-primary text-base font-semibold cursor-pointer text-white rounded-full py-2.5 px-4 flex items-center gap-2"
              >
                Create Gift Registry
                <ArrowCircleRight2 variant="Bulk" color="#fff" size={20} />
              </button>
            </div>
          </div>

          <div className="pt-5 px-4 pb-4 rounded-xl bg-[linear-gradient(182.72deg,#FEF7F4_20.31%,#F5F6FE_97.2%)]">
            <div className="text-2xl font-bold mb-1.5">
              {accountDetails.accountNumber}
            </div>
            <div>
              <span className="text-cus-orange-700 font-medium text-sm">
                {accountDetails.bank}{" "}
              </span>
              <span className="text-grey-950 text-sm italic font-bold">
                • {accountDetails.accountName}
              </span>
            </div>
            <div className="flex items-center text-sm italic text-dark-blue-200 gap-2 mt-4.5">
              <Icon name="bus" />
              <span>{accountDetails.location}</span>
            </div>
          </div>

          {/* Tab Navigation */}
          <div className="flex gap-1 mt-8 mb-4  p-1 rounded-full w-fit">
            <button
              onClick={() => setActiveTab("gift-items")}
              className={`px-6 py-2 rounded-full text-sm font-medium transition-all duration-200 ${
                activeTab === "gift-items"
                  ? "bg-primary text-white shadow-sm"
                  : "text-gray-600 hover:text-gray-800"
              }`}
            >
              Gift Items
            </button>
            <button
              onClick={() => setActiveTab("cash-gifts")}
              className={`px-6 py-2 rounded-full text-sm font-medium transition-all duration-200 ${
                activeTab === "cash-gifts"
                  ? "bg-primary text-white shadow-sm"
                  : "text-gray-600 hover:text-gray-800"
              }`}
            >
              Cash Gifts
            </button>
          </div>

          {/* Tab Content */}
          {activeTab === "gift-items" && (
            <div>
              {initialData.giftItems && initialData.giftItems.length > 0 ? (
                <div className="space-y-4">
                  {initialData.giftItems.map((item, index) => (
                    <div
                      key={index}
                      className="flex items-center md:h-[180px] py-3 sm:py-0 flex-col md:flex-row border gap-4 border-grey-150 rounded-[14px] relative"
                    >
                      <img
                        src={
                          typeof item.image === "string"
                            ? item.image
                            : item.image instanceof File
                            ? URL.createObjectURL(item.image)
                            : ""
                        }
                        alt={item.name}
                        className="max-w-[155px] w-full h-full md:rounded-l-[14px]"
                      />
                      <div className="h-full w-full p-2">
                        <div className="flex justify-end ">
                          <button className="cursor-pointer hover:opacity-70 transition-opacity">
                            <CloseCircle
                              size={28}
                              variant="Bulk"
                              color="#9499F7"
                            />
                          </button>
                        </div>
                        <div>
                          <h2 className="text-[22px] text-grey-750 font-medium ">
                            {item.name}
                          </h2>
                          <p className="text-grey-100 my-1 max-w-[300px] truncate">
                            {item.description}
                          </p>
                        </div>
                        <div className="mt-4 flex items-center gap-1.5 bg-light-blue-150 w-fit py-1.5 px-2.5 rounded-2xl">
                          <Tag2 size={12} variant="Bulk" color="#5925DC " />
                          <span className="text-perple text-sm font-medium">
                            ₦
                            {item.price
                              ? typeof item.price === "string"
                                ? parseFloat(
                                    item.price.replace(/,/g, "")
                                  ).toLocaleString()
                                : Number(item.price).toLocaleString()
                              : "0"}
                          </span>
                        </div>
                        <div className="flex justify-end mb-3">
                          {item.mostWanted === true && (
                            <div className=" bg-primary-150 text-primary-750 text-xs font-bold px-2 py-1 rounded-full flex items-center z-10 italic">
                              📍 MOST WANTED
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center py-12 px-4">
                  <div className="w-24 h-24 mb-4 opacity-50 bg-gray-200 rounded-lg flex items-center justify-center">
                    <Tag2 size={32} color="#9CA3AF" />
                  </div>
                  <h3 className="text-xl font-medium text-gray-600 mb-2">
                    No Gift Items Added
                  </h3>
                  <p className="text-gray-500 text-center">
                    You haven't added any gift items yet. Go back to add some
                    items to your registry.
                  </p>
                </div>
              )}
            </div>
          )}

          {activeTab === "cash-gifts" && (
            <div>
              {initialData.cashGifts && initialData.cashGifts.length > 0 ? (
                <div className="grid grid-cols-2 gap-4">
                  {initialData.cashGifts.map((cashGift) => (
                    <div
                      key={cashGift.id}
                      className="bg-[#F5F9FF] h-[259px] rounded-xl p-4 relative"
                    >
                      <button className="absolute top-2 right-2">
                        <CloseCircle color="#365B96" variant="Bulk" size={28} />
                      </button>
                      <img
                        src={stackMoney}
                        alt="Cash gift"
                        className="w-[104px] h-[88px] mt-4 mb-2"
                      />
                      <p className="text-[32px] font-bold">
                        ₦{Number(cashGift.amount).toLocaleString("en-NG")}
                      </p>
                      <p className="text-dark-blue-300 text-base italic mt-3">
                        {cashGift.description}
                      </p>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center py-12 px-4">
                  <img
                    src={stackMoney}
                    alt="No cash gifts"
                    className="w-24 h-24 mb-4 opacity-50"
                  />
                  <h3 className="text-xl font-medium text-gray-600 mb-2">
                    No Cash Gifts Added
                  </h3>
                  <p className="text-gray-500 text-center">
                    You haven't added any cash gifts yet. Go back to add some
                    cash gifts to your registry.
                  </p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
      {open && (
        <Success
          onClose={onClose}
          itemCount={initialData.giftItems?.length || 0}
          cashCount={initialData.cashGifts?.length || 0}
        />
      )}
    </>
  );
};
