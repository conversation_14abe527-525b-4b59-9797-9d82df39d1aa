# useEventManagement Hook - Updated Usage Guide

## Overview

The `useEventManagement` hook has been updated to support fetching complete event data using the `getEventByID` API endpoint. This provides access to comprehensive event information beyond the basic data returned by the events list API.

## Key Changes

### 1. New Data Types

- **`CompleteEventData`**: Interface for complete event data with all fields from the API
- **`completeSelectedEvent`**: State that holds the complete event data for the selected event

### 2. New Functions

- **`setSelectedEventWithDetails(event)`**: Enhanced version that automatically fetches complete details
- **`fetchCompleteEventDetails(eventId)`**: Directly fetch complete event data by ID
- **`isCompleteEventData(event)`**: Utility to check if event data is complete

## Usage Examples

### Basic Usage - Automatic Complete Data Fetching

```typescript
const { 
  selectedEvent, 
  completeSelectedEvent, 
  setSelectedEventWithDetails 
} = useEventManagement();

// When selecting an event, this will automatically fetch complete details
const handleEventSelect = (event: CreatedEventData) => {
  setSelectedEventWithDetails(event);
};

// Access complete event data
useEffect(() => {
  if (completeSelectedEvent) {
    console.log('Complete event data:', completeSelectedEvent);
    // Access additional fields like:
    // - completeSelectedEvent.location_name
    // - completeSelectedEvent.gift_registry_status
    // - completeSelectedEvent.guest_count
    // - completeSelectedEvent.images
    // etc.
  }
}, [completeSelectedEvent]);
```

### Manual Complete Data Fetching

```typescript
const { fetchCompleteEventDetails } = useEventManagement();

const loadCompleteEventData = async (eventId: string) => {
  const completeEvent = await fetchCompleteEventDetails(eventId);
  if (completeEvent) {
    // Use the complete event data
    console.log('Location:', completeEvent.location_name);
    console.log('Gift Registry Status:', completeEvent.gift_registry_status);
  }
};
```

### Checking Data Completeness

```typescript
const { selectedEvent, isCompleteEventData } = useEventManagement();

if (selectedEvent && isCompleteEventData(selectedEvent)) {
  // selectedEvent is guaranteed to be CompleteEventData
  console.log('Location name:', selectedEvent.location_name);
} else {
  // selectedEvent is basic CreatedEventData
  console.log('Basic event data only');
}
```

## Available Complete Event Data Fields

The `CompleteEventData` interface includes:

### Basic Info
- `id`, `title`, `description`, `category_id`, `category_name`
- `event_status`, `visibility`, `user_id`

### Dates
- `date_from`, `date_to`, `created_at`, `updated_at`

### Location
- `location_name`, `location_address`, `location_lat`, `location_long`

### Delivery
- `delivery_address`, `delivery_address_id`, `delivery_address_name`
- `delivery_address_lat`, `delivery_address_long`

### Images
- `banner_image_id`, `banner_preview_key`, `banner_preview_url`
- `images[]` array with preview keys and URLs

### Invitation
- `iv_preview_key`, `iv_preview_url`, `iv_template_id`
- `iv_template_modifications`, `iv_type`

### Gift Registry
- `gift_registry_status`, `gift_registry_title`, `most_wanted_gift_id`
- `gift_count` object with total, pending, approved counts

### Guest Management
- `guestlist_status`
- `guest_count` object with total, pending, confirmed, declined counts

### Other
- `reminders[]` array with reminder details

## Backward Compatibility

The hook maintains full backward compatibility:
- Existing `selectedEvent` and `setSelectedEvent` continue to work
- All existing optimistic update functions work as before
- Basic event data is still available through `selectedEvent`

## Performance Considerations

- Complete event data is fetched only when needed
- Data is cached in the store to avoid redundant API calls
- The hook intelligently detects if complete data is already available
